#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create additional test orders for optimization testing
"""

import os
import sys
import django
from datetime import datetime, timedelta, time
import random

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'optikarburant.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    django.setup()
    from django.utils import timezone
    from logistics.models import <PERSON>rosi, Stacion, Produkt, Depozite
    print("✓ Django setup successful")
except Exception as e:
    print(f"✗ Django setup failed: {e}")
    sys.exit(1)


def create_test_orders():
    """Create test orders for optimization"""
    print("📋 Creating test orders for optimization...")
    
    # Test orders data
    test_orders = [
        {'station_code': 'TIR001', 'product': 'Benzinë 95', 'quantity': 4000, 'priority': 'e_larte'},
        {'station_code': 'DUR001', 'product': 'Naftë D2', 'quantity': 6000, 'priority': 'normale'},
        {'station_code': 'SHK001', 'product': 'Benzinë 95', 'quantity': 3500, 'priority': 'e_larte'},
        {'station_code': 'ELB001', 'product': 'Gaz i Lëngshëm (LPG)', 'quantity': 2000, 'priority': 'normale'},
        {'station_code': 'VLO001', 'product': 'Naftë D2', 'quantity': 5500, 'priority': 'e_larte'},
        {'station_code': 'TIR002', 'product': 'Benzinë 100', 'quantity': 3000, 'priority': 'normale'},
    ]
    
    orders_created = 0
    
    for i, order_data in enumerate(test_orders):
        try:
            # Find station by code
            station = Stacion.objects.filter(kodi=order_data['station_code']).first()
            if not station:
                print(f"  ⚠ Station with code {order_data['station_code']} not found")
                continue
            
            # Find product
            product = Produkt.objects.filter(emri=order_data['product']).first()
            if not product:
                print(f"  ⚠ Product {order_data['product']} not found")
                continue
            
            # Find or create tank
            tank = Depozite.objects.filter(stacion=station, produkt=product).first()
            if not tank:
                # Create tank
                tank = Depozite.objects.create(
                    stacion=station,
                    produkt=product,
                    numri_tankut=f"T{i+1:02d}",
                    kapaciteti_total=15000,
                    sasia_aktuale=2000,  # Low level
                    niveli_minimal_sigurise=1500,
                    niveli_i_porosise=3000,
                    konsumi_mesatar_ditor=500,
                    sasia_minimale_dorezimi=2000,
                    perqindja_maksimale_mbushjes=95,
                )
                print(f"  ✓ Created tank for {station.emri} - {product.emri}")
            
            # Check if order already exists
            existing_order = Porosi.objects.filter(
                stacion=station,
                produkt=product,
                statusi__in=['e_hapur', 'e_planifikuar']
            ).first()
            
            if existing_order:
                print(f"  ⚠ Order already exists for {station.emri} - {product.emri}")
                continue
            
            # Create order with manual number to avoid conflicts
            today = timezone.now().strftime('%Y%m%d')
            order_number = f"TEST-{today}-{i+1:03d}"
            
            due_date = timezone.now() + timedelta(days=random.randint(1, 2))
            
            order = Porosi(
                numri_porosise=order_number,  # Set manually
                stacion=station,
                produkt=product,
                depozite=tank,
                sasia_e_kerkuar=order_data['quantity'],
                sasia_e_miratuar=order_data['quantity'],
                prioriteti=order_data['priority'],
                data_afati=due_date,
                koha_preferuar_fillimi=time(8, 0),
                koha_preferuar_mbarimi=time(16, 0),
                statusi='e_hapur',
                eshte_automatike=False,
                shenimet=f"Test order for optimization - {order_data['priority']} priority"
            )
            
            # Save without triggering auto-generation
            order.save()
            
            orders_created += 1
            print(f"  ✓ Created order {order_number}: {station.emri} - {product.emri} ({order_data['quantity']}L)")
            
        except Exception as e:
            print(f"  ⚠ Error creating order {i+1}: {e}")
    
    print(f"✅ Created {orders_created} test orders")
    return orders_created


def show_orders():
    """Show all orders in the system"""
    print("\n📋 All Orders in System:")
    orders = Porosi.objects.all().select_related('stacion', 'produkt')
    
    for order in orders:
        status_icon = "🟢" if order.statusi == 'e_hapur' else "🟡" if order.statusi == 'e_planifikuar' else "🔴"
        priority_icon = "🔥" if order.prioriteti == 'e_larte' else "⚡" if order.prioriteti == 'kritike' else "📋"
        
        print(f"  {status_icon} {priority_icon} {order.numri_porosise}: {order.stacion.emri}")
        print(f"      → {order.produkt.emri} ({order.sasia_e_kerkuar:.0f}L) - {order.get_statusi_display()}")


def main():
    """Main function"""
    print("🇦🇱 OptiKarburant - Create Test Orders")
    print("=" * 45)
    
    try:
        # Show current orders
        print("📊 Current orders:")
        current_orders = Porosi.objects.count()
        open_orders = Porosi.objects.filter(statusi='e_hapur').count()
        print(f"  • Total orders: {current_orders}")
        print(f"  • Open orders: {open_orders}")
        
        # Create test orders
        print("\n" + "=" * 45)
        orders_created = create_test_orders()
        
        # Show all orders
        show_orders()
        
        print(f"\n✅ Test orders creation completed!")
        print(f"📊 Summary:")
        print(f"   • New orders created: {orders_created}")
        print(f"   • Total orders now: {Porosi.objects.count()}")
        print(f"   • Open orders now: {Porosi.objects.filter(statusi='e_hapur').count()}")
        
        print(f"\n🚀 Ready for optimization testing!")
        print(f"   Run: docker exec -it optikarburant_web python test_optimization.py")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
