#!/usr/bin/env python
"""
Simple script to test route optimization without <PERSON><PERSON><PERSON>
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'optikarburant.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    django.setup()
    from django.utils import timezone
    from logistics.models import Porosi, Kamion, PlanRruge, Stacion
    print("✓ Django setup successful")
except Exception as e:
    print(f"✗ Django setup failed: {e}")
    sys.exit(1)


def simple_route_optimization():
    """Simple route optimization without OR-Tools"""
    print("🚛 Simple Route Optimization Test")
    print("=" * 40)
    
    # Get open orders
    open_orders = Porosi.objects.filter(statusi='e_hapur').select_related(
        'stacion', 'produkt', 'depozite'
    )
    
    print(f"📋 Found {open_orders.count()} open orders:")
    for order in open_orders:
        print(f"  • {order.stacion.emri} - {order.produkt.emri} ({order.sasia_e_kerkuar:.0f}L)")
    
    # Get available trucks
    available_trucks = Kamion.objects.filter(
        statusi='i_lire',
        eshte_aktiv=True
    ).select_related('shofer_aktual')
    
    print(f"\n🚚 Found {available_trucks.count()} available trucks:")
    for truck in available_trucks:
        driver_name = truck.shofer_aktual.emri if truck.shofer_aktual else "No driver"
        print(f"  • {truck.targa} - {truck.modeli} (Driver: {driver_name})")
    
    if not open_orders.exists():
        print("\n⚠ No open orders found. Cannot create routes.")
        return
    
    if not available_trucks.exists():
        print("\n⚠ No available trucks found. Cannot create routes.")
        return
    
    # Simple assignment: one truck per order (for testing)
    print(f"\n🗺 Creating simple routes...")
    
    tomorrow = timezone.now().date() + timedelta(days=1)
    routes_created = 0
    
    for i, order in enumerate(open_orders[:available_trucks.count()]):
        truck = available_trucks[i]
        
        try:
            # Create route plan
            route = PlanRruge.objects.create(
                data_planifikimit=tomorrow,
                kamion=truck,
                shofer=truck.shofer_aktual,
                koha_nisjes_nga_depo=timezone.now().replace(hour=8, minute=0, second=0, microsecond=0) + timedelta(days=1),
                statusi='draft',
                shenimet=f"Simple test route for order {order.numri_porosise}"
            )
            
            print(f"  ✓ Created route {route.numri_rrugese} for truck {truck.targa}")
            print(f"    → Delivery: {order.stacion.emri} ({order.sasia_e_kerkuar:.0f}L {order.produkt.emri})")
            
            # Update order status
            order.statusi = 'e_planifikuar'
            order.save()
            
            routes_created += 1
            
        except Exception as e:
            print(f"  ⚠ Error creating route for {truck.targa}: {e}")
    
    print(f"\n✅ Created {routes_created} test routes!")
    print(f"📊 Summary:")
    print(f"   • Routes created: {routes_created}")
    print(f"   • Orders planned: {routes_created}")
    print(f"   • Date: {tomorrow}")
    
    # Show created routes
    created_routes = PlanRruge.objects.filter(data_planifikimit=tomorrow)
    print(f"\n📋 Created Routes:")
    for route in created_routes:
        print(f"  • {route.numri_rrugese}: {route.kamion.targa} - {route.shofer.emri if route.shofer else 'No driver'}")


def show_current_data():
    """Show current data in the system"""
    print("📊 Current System Data")
    print("=" * 30)
    
    # Products
    from logistics.models import Produkt, DepoQendrore, Depozite
    products = Produkt.objects.filter(eshte_aktiv=True)
    print(f"🛢 Products: {products.count()}")
    for product in products:
        print(f"  • {product.emri}")
    
    # Depot
    depot = DepoQendrore.objects.filter(eshte_aktiv=True).first()
    print(f"\n🏭 Central Depot: {depot.emri if depot else 'None'}")
    
    # Stations
    stations = Stacion.objects.filter(eshte_aktiv=True)
    print(f"\n⛽ Stations: {stations.count()}")
    for station in stations[:5]:  # Show first 5
        print(f"  • {station.emri} ({station.kodi})")
    if stations.count() > 5:
        print(f"  ... and {stations.count() - 5} more")
    
    # Trucks
    trucks = Kamion.objects.filter(eshte_aktiv=True)
    print(f"\n🚚 Trucks: {trucks.count()}")
    for truck in trucks:
        status = truck.get_statusi_display()
        print(f"  • {truck.targa} - {truck.modeli} ({status})")
    
    # Orders
    orders = Porosi.objects.all()
    print(f"\n📋 Orders: {orders.count()}")
    for order in orders:
        print(f"  • {order.numri_porosise}: {order.stacion.emri} - {order.produkt.emri} ({order.get_statusi_display()})")
    
    # Routes
    routes = PlanRruge.objects.all()
    print(f"\n🗺 Routes: {routes.count()}")
    for route in routes:
        print(f"  • {route.numri_rrugese}: {route.kamion.targa} ({route.get_statusi_display()})")


def create_more_test_orders():
    """Create additional test orders for better optimization testing"""
    print("📋 Creating additional test orders...")

    from logistics.models import Produkt, Depozite
    from datetime import time

    # Test orders data
    test_orders = [
        {'station_name': 'Stacioni Qender Tiranë', 'product': 'Benzinë 95', 'quantity': 4000, 'priority': 'e_larte'},
        {'station_name': 'Stacioni Durrës Port', 'product': 'Naftë D2', 'quantity': 6000, 'priority': 'normale'},
        {'station_name': 'Stacioni Shkodër Qender', 'product': 'Benzinë 95', 'quantity': 3500, 'priority': 'e_larte'},
        {'station_name': 'Stacioni Elbasan Qender', 'product': 'Gaz i Lëngshëm (LPG)', 'quantity': 2000, 'priority': 'normale'},
        {'station_name': 'Stacioni Vlorë Port', 'product': 'Naftë D2', 'quantity': 5500, 'priority': 'e_larte'},
    ]

    orders_created = 0
    for order_data in test_orders:
        try:
            station = Stacion.objects.filter(emri=order_data['station_name']).first()
            product = Produkt.objects.filter(emri=order_data['product']).first()

            if station and product:
                # Find or create a tank for this product at this station
                tank = Depozite.objects.filter(stacion=station, produkt=product).first()

                if not tank:
                    # Create a tank if it doesn't exist
                    tank = Depozite.objects.create(
                        stacion=station,
                        produkt=product,
                        numri_tankut="T01",
                        kapaciteti_total=15000,
                        sasia_aktuale=2000,  # Low level to trigger order
                        niveli_minimal_sigurise=1500,
                        niveli_i_porosise=3000,
                        konsumi_mesatar_ditor=500,
                        sasia_minimale_dorezimi=2000,
                        perqindja_maksimale_mbushjes=95,
                    )

                # Check if order already exists
                existing_order = Porosi.objects.filter(
                    stacion=station,
                    produkt=product,
                    depozite=tank,
                    statusi__in=['e_hapur', 'e_planifikuar', 'ne_transport']
                ).first()

                if not existing_order:
                    due_date = timezone.now() + timedelta(days=1)

                    order = Porosi.objects.create(
                        stacion=station,
                        produkt=product,
                        depozite=tank,
                        sasia_e_kerkuar=order_data['quantity'],
                        sasia_e_miratuar=order_data['quantity'],
                        prioriteti=order_data['priority'],
                        data_afati=due_date,
                        koha_preferuar_fillimi=time(8, 0),
                        koha_preferuar_mbarimi=time(16, 0),
                        statusi='e_hapur',
                        eshte_automatike=False,
                        shenimet=f"Test order - {order_data['priority']} priority"
                    )
                    orders_created += 1
                    print(f"  ✓ Created order: {station.emri} - {product.emri} ({order_data['quantity']}L)")
                else:
                    print(f"  ⚠ Order already exists for {station.emri} - {product.emri}")
            else:
                print(f"  ⚠ Station or product not found for {order_data['station_name']} - {order_data['product']}")

        except Exception as e:
            print(f"  ⚠ Error creating order: {e}")

    print(f"✓ Created {orders_created} additional test orders")
    return orders_created


def main():
    """Main function"""
    print("🇦🇱 OptiKarburant - Route Optimization Test")
    print("=" * 50)

    try:
        # Show current data
        show_current_data()

        print("\n" + "=" * 50)

        # Create more test orders
        create_more_test_orders()

        print("\n" + "=" * 50)

        # Test simple optimization
        simple_route_optimization()

        print("\n🚀 Test completed! Check Django admin to see the results.")
        print("🌐 Admin panel: http://localhost:8000/admin")
        print("📊 API endpoints: http://localhost:8000/api/")
        print("🗺 Routes: http://localhost:8000/api/routes/")
        print("📋 Orders: http://localhost:8000/api/orders/")

    except Exception as e:
        print(f"\n❌ Error during optimization test: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
