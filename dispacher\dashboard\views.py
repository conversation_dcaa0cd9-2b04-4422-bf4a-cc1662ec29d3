# dashboard/views.py - Main dashboard views for OptiKarburant

from datetime import datetime, timedelta
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg, F
from django.core.paginator import Paginator
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json

from logistics.models import (
    Stacion, Depozite, Porosi, Kamion, PlanRruge, 
    Produkt, Depo<PERSON>end<PERSON>re, <PERSON><PERSON><PERSON>
)


@login_required
def home_dashboard(request):
    """Main dashboard showing key metrics and alerts"""
    
    # Key Performance Indicators
    total_stations = Stacion.objects.filter(eshte_aktiv=True).count()
    active_trucks = Kamion.objects.filter(
        statusi='i_lire', 
        eshte_aktiv=True
    ).count()
    
    open_orders = Porosi.objects.filter(statusi='e_hapur').count()
    
    # Critical tanks (below safety level)
    critical_tanks = Depozite.objects.filter(
        sasia_aktuale__lte=F('niveli_minimal_sigurise')
    ).count()
    
    # Today's routes
    today = timezone.now().date()
    todays_routes = PlanRruge.objects.filter(
        data_planifikimit=today,
        statusi__in=['miratuar', 'ne_progres']
    ).count()
    
    # Recent alerts
    recent_alerts = get_recent_alerts()
    
    # Today's route details for display
    todays_routes_detail = PlanRruge.objects.filter(
        data_planifikimit=today
    ).select_related('kamion', 'shofer').prefetch_related('ndalesat')[:5]
    
    # Calculate progress for each route
    for route in todays_routes_detail:
        total_stops = route.ndalesat.count()
        completed_stops = route.ndalesat.filter(eshte_perfunduar=True).count()
        route.progress_percentage = (
            (completed_stops / total_stops * 100) if total_stops > 0 else 0
        )
    
    # Weekly statistics
    week_start = today - timedelta(days=today.weekday())
    weekly_stats = {
        'routes_completed': PlanRruge.objects.filter(
            data_planifikimit__gte=week_start,
            statusi='perfunduar'
        ).count(),
        'orders_delivered': Porosi.objects.filter(
            statusi='e_dorezuar',
            data_perditesimit__date__gte=week_start
        ).count(),
        'distance_traveled': PlanRruge.objects.filter(
            data_planifikimit__gte=week_start,
            statusi='perfunduar'
        ).aggregate(
            total=Sum('distanca_aktuale_km')
        )['total'] or 0
    }
    
    context = {
        'page_title': _('Dashboard'),
        'total_stations': total_stations,
        'active_trucks': active_trucks,
        'open_orders': open_orders,
        'critical_tanks': critical_tanks,
        'todays_routes': todays_routes,
        'recent_alerts': recent_alerts,
        'todays_routes_detail': todays_routes_detail,
        'weekly_stats': weekly_stats,
    }
    
    return render(request, 'dashboard/home.html', context)


@login_required
def station_overview(request):
    """Station overview with map and status"""
    
    # Get all active stations with tank information
    stations = Stacion.objects.filter(
        eshte_aktiv=True
    ).prefetch_related('depozitat__produkt').annotate(
        total_tanks=Count('depozitat'),
        critical_tanks=Count(
            'depozitat',
            filter=Q(depozitat__sasia_aktuale__lte=F('depozitat__niveli_minimal_sigurise'))
        ),
        low_tanks=Count(
            'depozitat',
            filter=Q(depozitat__sasia_aktuale__lte=F('depozitat__niveli_i_porosise'))
        )
    )
    
    # Filter by region if specified
    region = request.GET.get('region')
    if region:
        stations = stations.filter(adresa__icontains=region)
    
    # Search functionality
    search = request.GET.get('search')
    if search:
        stations = stations.filter(
            Q(emri__icontains=search) |
            Q(kodi__icontains=search) |
            Q(adresa__icontains=search)
        )
    
    # Pagination
    paginator = Paginator(stations, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Prepare stations data for map
    stations_map_data = []
    for station in page_obj:
        # Calculate overall status
        if station.critical_tanks > 0:
            status = 'critical'
            status_color = '#dc3545'  # Red
        elif station.low_tanks > 0:
            status = 'warning'
            status_color = '#ffc107'  # Yellow
        else:
            status = 'normal'
            status_color = '#28a745'  # Green
        
        stations_map_data.append({
            'id': station.id,
            'name': station.emri,
            'code': station.kodi,
            'lat': float(station.vendndodhja.y),
            'lng': float(station.vendndodhja.x),
            'status': status,
            'color': status_color,
            'total_tanks': station.total_tanks,
            'critical_tanks': station.critical_tanks,
            'address': station.adresa,
        })
    
    context = {
        'page_title': _('Station Overview'),
        'stations': page_obj,
        'stations_map_data': stations_map_data,
        'search': search,
        'region': region,
        'total_stations': stations.count(),
    }
    
    return render(request, 'dashboard/station_overview.html', context)


@login_required
def fleet_overview(request):
    """Fleet overview showing truck status and utilization"""
    
    # Get all trucks with current status
    trucks = Kamion.objects.filter(
        eshte_aktiv=True
    ).select_related('shofer_aktual').prefetch_related('particionet')
    
    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        trucks = trucks.filter(statusi=status_filter)
    
    # Fleet statistics
    fleet_stats = Kamion.objects.filter(eshte_aktiv=True).aggregate(
        total_trucks=Count('id'),
        available_trucks=Count('id', filter=Q(statusi='i_lire')),
        en_route_trucks=Count('id', filter=Q(statusi='ne_rruge')),
        maintenance_trucks=Count('id', filter=Q(statusi='mirembajtje')),
        out_of_service=Count('id', filter=Q(statusi='jashte_sherbimi')),
    )
    
    # Calculate utilization rate for this week
    week_start = timezone.now().date() - timedelta(days=timezone.now().weekday())
    weekly_utilization = PlanRruge.objects.filter(
        data_planifikimit__gte=week_start
    ).aggregate(
        avg_distance=Avg('distanca_e_planifikuar_km'),
        avg_duration=Avg('kohezgjatja_e_planifikuar_ore'),
        total_routes=Count('id')
    )
    
    # Maintenance alerts
    maintenance_alerts = Kamion.objects.filter(
        eshte_aktiv=True,
        odometri_aktual_km__gte=F('km_mirembajtjes_rradheses')
    ).exclude(km_mirembajtjes_rradheses__isnull=True)
    
    context = {
        'page_title': _('Fleet Overview'),
        'trucks': trucks,
        'fleet_stats': fleet_stats,
        'weekly_utilization': weekly_utilization,
        'maintenance_alerts': maintenance_alerts,
        'status_choices': Kamion.STATUS_CHOICES,
        'status_filter': status_filter,
    }
    
    return render(request, 'dashboard/fleet_overview.html', context)


@login_required
def order_management(request):
    """Order management dashboard"""
    
    # Get orders with filtering
    orders = Porosi.objects.select_related(
        'stacion', 'produkt', 'depozite', 'krijuar_nga'
    ).order_by('-data_krijimit')
    
    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        orders = orders.filter(statusi=status_filter)
    
    # Filter by priority
    priority_filter = request.GET.get('priority')
    if priority_filter:
        orders = orders.filter(prioriteti=priority_filter)
    
    # Filter by date range
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            orders = orders.filter(data_krijimit__date__gte=date_from)
        except ValueError:
            date_from = None
    
    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
            orders = orders.filter(data_krijimit__date__lte=date_to)
        except ValueError:
            date_to = None
    
    # Pagination
    paginator = Paginator(orders, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Order statistics
    order_stats = Porosi.objects.aggregate(
        total_orders=Count('id'),
        open_orders=Count('id', filter=Q(statusi='e_hapur')),
        planned_orders=Count('id', filter=Q(statusi='e_planifikuar')),
        delivered_orders=Count('id', filter=Q(statusi='e_dorezuar')),
        total_quantity=Sum('sasia_e_kerkuar'),
        urgent_orders=Count('id', filter=Q(prioriteti__in=['e_larte', 'kritike']))
    )
    
    # Get active stations and products for the new order form
    active_stations = Stacion.objects.filter(eshte_aktiv=True).order_by('emri')
    active_products = Produkt.objects.filter(eshte_aktiv=True).order_by('emri')

    context = {
        'page_title': _('Order Management'),
        'orders': page_obj,
        'order_stats': order_stats,
        'status_choices': Porosi.STATUS_CHOICES,
        'priority_choices': Porosi.PRIORITY_CHOICES,
        'active_stations': active_stations,
        'active_products': active_products,
        'filters': {
            'status': status_filter,
            'priority': priority_filter,
            'date_from': date_from.strftime('%Y-%m-%d') if date_from else '',
            'date_to': date_to.strftime('%Y-%m-%d') if date_to else '',
        }
    }
    
    return render(request, 'dashboard/order_management.html', context)


@login_required
def analytics_dashboard(request):
    """Analytics and reporting dashboard"""
    
    # Date range for analysis (default: last 30 days)
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)
    
    # Performance metrics
    route_performance = PlanRruge.objects.filter(
        data_planifikimit__range=[start_date, end_date],
        statusi='perfunduar'
    ).aggregate(
        total_routes=Count('id'),
        total_distance=Sum('distanca_aktuale_km'),
        total_duration=Sum('kohezgjatja_aktuale_ore'),
        avg_efficiency=Avg(F('kohezgjatja_e_planifikuar_ore') / F('kohezgjatja_aktuale_ore') * 100),
        total_cost=Sum('kostoja_aktuale')
    )
    
    # Daily delivery trends
    daily_deliveries = []
    for i in range(30):
        date = end_date - timedelta(days=i)
        deliveries = Porosi.objects.filter(
            statusi='e_dorezuar',
            data_perditesimit__date=date
        ).aggregate(
            count=Count('id'),
            quantity=Sum('sasia_e_dorezuar')
        )
        daily_deliveries.append({
            'date': date.strftime('%Y-%m-%d'),
            'deliveries': deliveries['count'] or 0,
            'quantity': deliveries['quantity'] or 0
        })
    
    daily_deliveries.reverse()  # Show chronologically
    
    # Product distribution
    product_stats = Produkt.objects.annotate(
        total_orders=Count('porosi'),
        total_quantity=Sum('porosi__sasia_e_dorezuar'),
        active_tanks=Count('depozite')
    ).filter(eshte_aktiv=True)
    
    # Station performance
    station_performance = Stacion.objects.filter(
        eshte_aktiv=True
    ).annotate(
        orders_received=Count(
            'porosi',
            filter=Q(porosi__data_krijimit__range=[start_date, end_date])
        ),
        quantity_delivered=Sum(
            'porosi__sasia_e_dorezuar',
            filter=Q(porosi__data_perditesimit__range=[start_date, end_date])
        )
    ).order_by('-quantity_delivered')[:10]
    
    context = {
        'page_title': _('Analytics Dashboard'),
        'route_performance': route_performance,
        'daily_deliveries': daily_deliveries,
        'product_stats': product_stats,
        'station_performance': station_performance,
        'start_date': start_date,
        'end_date': end_date,
    }
    
    return render(request, 'dashboard/analytics.html', context)


def get_recent_alerts():
    """Get recent system alerts and notifications"""
    alerts = []
    
    # Critical tank levels
    critical_tanks = Depozite.objects.filter(
        sasia_aktuale__lte=F('niveli_minimal_sigurise')
    ).select_related('stacion', 'produkt')[:5]
    
    for tank in critical_tanks:
        alerts.append({
            'type': 'critical',
            'message': _('Tank {tank} at {station} is below safety level').format(
                tank=f"{tank.produkt.emri} Tank {tank.numri_tankut}",
                station=tank.stacion.emri
            ),
            'url': f'/dashboard/stations/{tank.stacion.id}/',
            'timestamp': tank.data_perditesimit,
        })
    
    # Overdue orders
    overdue_orders = Porosi.objects.filter(
        data_afati__lt=timezone.now(),
        statusi='e_hapur'
    ).select_related('stacion', 'produkt')[:5]
    
    for order in overdue_orders:
        alerts.append({
            'type': 'warning',
            'message': _('Order {order_num} for {station} is overdue').format(
                order_num=order.numri_porosise,
                station=order.stacion.emri
            ),
            'url': f'/dashboard/orders/{order.id}/',
            'timestamp': order.data_afati,
        })
    
    # Maintenance alerts
    maintenance_trucks = Kamion.objects.filter(
        eshte_aktiv=True,
        odometri_aktual_km__gte=F('km_mirembajtjes_rradheses')
    ).exclude(km_mirembajtjes_rradheses__isnull=True)[:3]
    
    for truck in maintenance_trucks:
        alerts.append({
            'type': 'info',
            'message': _('Truck {plate} needs maintenance').format(
                plate=truck.targa
            ),
            'url': f'/dashboard/fleet/{truck.id}/',
            'timestamp': timezone.now(),
        })
    
    return sorted(alerts, key=lambda x: x['timestamp'], reverse=True)


@login_required  
def api_station_status(request, station_id):
    """API endpoint for real-time station status"""
    
    try:
        station = get_object_or_404(Stacion, id=station_id, eshte_aktiv=True)
        
        tanks = station.depozitat.select_related('produkt').all()
        
        tank_data = []
        for tank in tanks:
            tank_data.append({
                'id': tank.id,
                'product': tank.produkt.emri,
                'tank_number': tank.numri_tankut,
                'current_quantity': float(tank.sasia_aktuale),
                'total_capacity': float(tank.kapaciteti_total),
                'fill_percentage': tank.perqindja_mbushjes,
                'needs_refill': tank.nevojitet_rifornizim,
                'is_critical': tank.eshte_kritik,
                'days_until_empty': tank.dite_deri_zbrazje if tank.dite_deri_zbrazje != float('inf') else None
            })
        
        return JsonResponse({
            'station': {
                'id': station.id,
                'name': station.emri,
                'code': station.kodi,
                'address': station.adresa,
            },
            'tanks': tank_data,
            'last_updated': timezone.now().isoformat()
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["POST"])
def create_order(request):
    """Create a new order via AJAX"""
    try:
        data = json.loads(request.body)

        # Validate required fields
        required_fields = ['station', 'product', 'quantity', 'priority']
        for field in required_fields:
            if not data.get(field):
                return JsonResponse({
                    'success': False,
                    'error': f'{field.title()} is required'
                }, status=400)

        # Get related objects
        try:
            station = Stacion.objects.get(id=data['station'])
            product = Produkt.objects.get(id=data['product'])

            # Find appropriate tank for this product at the station
            tank = Depozite.objects.filter(
                stacion=station,
                produkt=product,
                eshte_aktiv=True
            ).first()

            if not tank:
                return JsonResponse({
                    'success': False,
                    'error': f'No active tank found for {product.emri} at {station.emri}'
                }, status=400)

        except (Stacion.DoesNotExist, Produkt.DoesNotExist):
            return JsonResponse({
                'success': False,
                'error': 'Invalid station or product selected'
            }, status=400)

        # Validate quantity
        try:
            quantity = float(data['quantity'])
            if quantity <= 0:
                raise ValueError("Quantity must be positive")
        except (ValueError, TypeError):
            return JsonResponse({
                'success': False,
                'error': 'Invalid quantity value'
            }, status=400)

        # Map priority values
        priority_mapping = {
            'low': 'e_ulet',
            'medium': 'normale',
            'high': 'e_larte',
            'critical': 'kritike'
        }
        priority = priority_mapping.get(data['priority'], 'normale')

        # Parse delivery date if provided
        delivery_date = None
        if data.get('delivery_date'):
            try:
                delivery_date = datetime.fromisoformat(data['delivery_date'].replace('T', ' '))
            except ValueError:
                return JsonResponse({
                    'success': False,
                    'error': 'Invalid delivery date format'
                }, status=400)

        # Create the order
        order = Porosi.objects.create(
            stacion=station,
            produkt=product,
            depozite=tank,
            sasia_e_kerkuar=quantity,
            sasia_e_miratuar=quantity,
            prioriteti=priority,
            data_afati=delivery_date,
            statusi='e_hapur',
            eshte_automatike=False,
            shenimet=data.get('notes', ''),
            krijuar_nga=request.user
        )

        return JsonResponse({
            'success': True,
            'message': 'Order created successfully',
            'order': {
                'id': order.id,
                'number': order.numri_porosise,
                'station': station.emri,
                'product': product.emri,
                'quantity': quantity,
                'priority': order.get_prioriteti_display(),
                'status': order.get_statusi_display()
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Server error: {str(e)}'
        }, status=500)


@login_required
def get_stations_api(request):
    """Get all active stations for dropdown"""
    stations = Stacion.objects.filter(eshte_aktiv=True).values(
        'id', 'emri', 'adresa'
    ).order_by('emri')

    return JsonResponse({
        'success': True,
        'stations': list(stations)
    })


@login_required
def get_products_api(request):
    """Get all active products for dropdown"""
    products = Produkt.objects.filter(eshte_aktiv=True).values(
        'id', 'emri', 'densiteti'
    ).order_by('emri')

    return JsonResponse({
        'success': True,
        'products': list(products)
    })
