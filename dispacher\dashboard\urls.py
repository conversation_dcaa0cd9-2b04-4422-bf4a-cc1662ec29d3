# dashboard/urls.py - URL configuration for dashboard module

from django.urls import path
from . import views

app_name = 'dashboard'

urlpatterns = [
    # Main dashboard
    path('', views.home_dashboard, name='home'),
    
    # Overview pages
    path('stations/', views.station_overview, name='station_overview'),
    path('fleet/', views.fleet_overview, name='fleet_overview'),
    path('orders/', views.order_management, name='order_management'),
    path('analytics/', views.analytics_dashboard, name='analytics'),
    
    # API endpoints for real-time data
    path('api/station/<int:station_id>/status/', views.api_station_status, name='api_station_status'),

    # Order management API endpoints
    path('api/orders/create/', views.create_order, name='create_order'),
    path('api/stations/', views.get_stations_api, name='get_stations_api'),
    path('api/products/', views.get_products_api, name='get_products_api'),
]
