#!/usr/bin/env python
"""
Standalone script to populate OptiKarburant database with Albanian sample data
This script can be run independently of Django management commands
"""

import os
import sys
import django
from datetime import datetime, time, timedelta
import random

# Setup Django environment with PostGIS settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'optikarburant.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    django.setup()
    from django.contrib.gis.geos import Point
    from django.utils import timezone
    from logistics.models import (
        Produkt, DepoQendrore, Stacion, Depozite, Shofer, 
        Kamion, Particion, Porosi
    )
    print("✓ Django setup successful")
except Exception as e:
    print(f"✗ Django setup failed: {e}")
    print("This might be due to missing GDAL libraries.")
    print("Please install PostGIS and GDAL, or modify settings to use SQLite.")
    sys.exit(1)

def clear_existing_data():
    """Clear all existing data"""
    print("Clearing existing data...")
    Porosi.objects.all().delete()
    Particion.objects.all().delete()
    Kamion.objects.all().delete()
    Shofer.objects.all().delete()
    Depozite.objects.all().delete()
    Stacion.objects.all().delete()
    DepoQendrore.objects.all().delete()
    Produkt.objects.all().delete()
    print("✓ Existing data cleared")

def create_products():
    """Create Albanian fuel products"""
    print("Creating fuel products...")
    products_data = [
        {'emri': 'Naftë D2', 'densiteti': 0.85, 'ngjyra_kodi': '#2c3e50'},
        {'emri': 'Benzinë 95', 'densiteti': 0.75, 'ngjyra_kodi': '#e74c3c'},
        {'emri': 'Benzinë 100', 'densiteti': 0.75, 'ngjyra_kodi': '#c0392b'},
        {'emri': 'Gaz i Lëngshëm (LPG)', 'densiteti': 0.51, 'ngjyra_kodi': '#3498db'},
        {'emri': 'Naftë Ngrohje', 'densiteti': 0.87, 'ngjyra_kodi': '#8e44ad'},
    ]
    
    products = []
    for data in products_data:
        product, created = Produkt.objects.get_or_create(
            emri=data['emri'],
            defaults=data
        )
        products.append(product)
        if created:
            print(f"  ✓ Created product: {product.emri}")
    
    # Set product compatibility
    nafte = products[0]  # Naftë D2
    benzine_95 = products[1]  # Benzinë 95
    benzine_100 = products[2]  # Benzinë 100
    
    # Benzines are compatible with each other
    benzine_95.produkte_kompatible.add(benzine_100)
    benzine_100.produkte_kompatible.add(benzine_95)
    
    print(f"✓ Created {len(products)} products")
    return products

def create_central_depot():
    """Create central depot in Tirana"""
    print("Creating central depot...")
    depot, created = DepoQendrore.objects.get_or_create(
        emri='Depo Qendrore Tiranë',
        defaults={
            'vendndodhja': Point(19.8189, 41.3275),  # Tirana coordinates
            'adresa': 'Rruga Industriale, Tiranë, Shqipëri',
            'kapaciteti_ngarkimi': 8,
            'koha_mesatare_ngarkimi': 90,
            'orar_punes_nga': time(6, 0),
            'orar_punes_deri': time(22, 0),
        }
    )
    if created:
        print(f"  ✓ Created depot: {depot.emri}")
    print("✓ Central depot ready")
    return depot

def create_stations():
    """Create comprehensive Albanian fuel stations"""
    print("Creating fuel stations...")
    stations_data = [
        # Tirana area - Major distribution hub
        {'emri': 'Stacioni Qender Tiranë', 'kodi': 'TIR001', 'lat': 41.3275, 'lng': 19.8189, 'city': 'Tiranë', 'type': 'major'},
        {'emri': 'Stacioni Kombinat', 'kodi': 'TIR002', 'lat': 41.2911, 'lng': 19.8607, 'city': 'Tiranë', 'type': 'standard'},
        {'emri': 'Stacioni Don Bosko', 'kodi': 'TIR003', 'lat': 41.3151, 'lng': 19.8331, 'city': 'Tiranë', 'type': 'standard'},
        {'emri': 'Stacioni Kashar', 'kodi': 'TIR004', 'lat': 41.3847, 'lng': 19.7736, 'city': 'Tiranë', 'type': 'highway'},
        {'emri': 'Stacioni Kamëz', 'kodi': 'TIR005', 'lat': 41.3814, 'lng': 19.7631, 'city': 'Tiranë', 'type': 'standard'},
        
        # Durrës - Port city, high volume
        {'emri': 'Stacioni Durrës Port', 'kodi': 'DUR001', 'lat': 41.3147, 'lng': 19.4444, 'city': 'Durrës', 'type': 'major'},
        {'emri': 'Stacioni Durrës Qender', 'kodi': 'DUR002', 'lat': 41.3236, 'lng': 19.4581, 'city': 'Durrës', 'type': 'standard'},
        {'emri': 'Stacioni Durrës Plazh', 'kodi': 'DUR003', 'lat': 41.3200, 'lng': 19.4300, 'city': 'Durrës', 'type': 'seasonal'},
        
        # Other major cities
        {'emri': 'Stacioni Shkodër Qender', 'kodi': 'SHK001', 'lat': 42.0683, 'lng': 19.5122, 'city': 'Shkodër', 'type': 'major'},
        {'emri': 'Stacioni Elbasan Qender', 'kodi': 'ELB001', 'lat': 41.1125, 'lng': 20.0822, 'city': 'Elbasan', 'type': 'major'},
        {'emri': 'Stacioni Vlorë Port', 'kodi': 'VLO001', 'lat': 40.4686, 'lng': 19.4889, 'city': 'Vlorë', 'type': 'major'},
        {'emri': 'Stacioni Korçë Qender', 'kodi': 'KOR001', 'lat': 40.6186, 'lng': 20.7719, 'city': 'Korçë', 'type': 'major'},
        {'emri': 'Stacioni Fier Qender', 'kodi': 'FIE001', 'lat': 40.7239, 'lng': 19.5556, 'city': 'Fier', 'type': 'major'},
        {'emri': 'Stacioni Fier Industrial', 'kodi': 'FIE002', 'lat': 40.7300, 'lng': 19.5600, 'city': 'Fier', 'type': 'industrial'},
        
        # Additional stations
        {'emri': 'Stacioni Berat', 'kodi': 'BER001', 'lat': 40.7058, 'lng': 19.9522, 'city': 'Berat', 'type': 'standard'},
        {'emri': 'Stacioni Gjirokastër', 'kodi': 'GJI001', 'lat': 40.0758, 'lng': 20.1389, 'city': 'Gjirokastër', 'type': 'standard'},
        {'emri': 'Stacioni Lushnjë', 'kodi': 'LUS001', 'lat': 40.9419, 'lng': 19.7050, 'city': 'Lushnjë', 'type': 'standard'},
        {'emri': 'Stacioni Pogradec', 'kodi': 'POG001', 'lat': 40.9022, 'lng': 20.6528, 'city': 'Pogradec', 'type': 'seasonal'},
        {'emri': 'Stacioni Kukës', 'kodi': 'KUK001', 'lat': 42.0772, 'lng': 20.4214, 'city': 'Kukës', 'type': 'standard'},
        {'emri': 'Stacioni Lezhë', 'kodi': 'LEZ001', 'lat': 41.7836, 'lng': 19.6439, 'city': 'Lezhë', 'type': 'standard'},
    ]
    
    stations = []
    for data in stations_data:
        # Set operating parameters based on station type
        if data['type'] == 'major':
            start_time, end_time = time(5, 0), time(20, 0)
            max_trucks, unload_time = 2, 60
            requires_pump, requires_meter = True, True
        elif data['type'] == 'highway':
            start_time, end_time = time(0, 0), time(23, 59)
            max_trucks, unload_time = 3, 30
            requires_pump, requires_meter = False, True
        elif data['type'] == 'industrial':
            start_time, end_time = time(6, 0), time(18, 0)
            max_trucks, unload_time = 2, 90
            requires_pump, requires_meter = True, True
        elif data['type'] == 'seasonal':
            start_time, end_time = time(7, 0), time(19, 0)
            max_trucks, unload_time = 1, 45
            requires_pump, requires_meter = False, True
        else:  # standard
            start_time, end_time = time(6, 0), time(18, 0)
            max_trucks, unload_time = 1, 45
            requires_pump, requires_meter = False, True
        
        station, created = Stacion.objects.get_or_create(
            kodi=data['kodi'],
            defaults={
                'emri': data['emri'],
                'vendndodhja': Point(data['lng'], data['lat']),
                'adresa': f"{data['emri']}, {data['city']}, Shqipëri",
                'orar_pranimi_nga': start_time,
                'orar_pranimi_deri': end_time,
                'kerkon_pompe': requires_pump,
                'kerkon_kontaliter': requires_meter,
                'max_kamione_njekohesisht': max_trucks,
                'koha_mesatare_shkarkimi': unload_time,
                'max_pesha_kamioni_ton': 40.0 if data['type'] != 'seasonal' else 25.0,
                'max_gjatesia_kamioni_m': 16.5 if data['type'] != 'seasonal' else 12.0,
                'menaxher_emri': f"Menaxher {data['city']}",
                'telefoni': f"+35569{random.randint(1000000, 9999999)}",
            }
        )
        stations.append(station)
        if created:
            print(f"  ✓ Created station: {station.emri}")
    
    print(f"✓ Created {len(stations)} stations")
    return stations

def create_drivers():
    """Create Albanian truck drivers"""
    print("Creating drivers...")

    drivers_data = [
        {'emri': 'Agim', 'mbiemri': 'Hoxha', 'telefoni': '+35569123456', 'adr': True},
        {'emri': 'Besnik', 'mbiemri': 'Kola', 'telefoni': '+35569234567', 'adr': True},
        {'emri': 'Dritan', 'mbiemri': 'Rama', 'telefoni': '+35569345678', 'adr': False},
        {'emri': 'Ermal', 'mbiemri': 'Duka', 'telefoni': '+35569456789', 'adr': True},
        {'emri': 'Flamur', 'mbiemri': 'Basha', 'telefoni': '+35569567890', 'adr': False},
        {'emri': 'Gentian', 'mbiemri': 'Meta', 'telefoni': '+35569678901', 'adr': True},
        {'emri': 'Ilir', 'mbiemri': 'Nano', 'telefoni': '+35569789012', 'adr': False},
        {'emri': 'Klodian', 'mbiemri': 'Shehu', 'telefoni': '+35569890123', 'adr': True},
    ]

    drivers = []
    for i, data in enumerate(drivers_data):
        try:
            # Generate license expiry date (6 months to 2 years from now)
            expiry_days = random.randint(180, 730)
            expiry_date = timezone.now().date() + timedelta(days=expiry_days)

            driver, created = Shofer.objects.get_or_create(
                leje_drejtimi_numri=f"AL{2024000 + i + 1:06d}",
                defaults={
                    'emri': data['emri'],
                    'mbiemri': data['mbiemri'],
                    'telefoni': data['telefoni'],
                    'email': f"{data['emri'].lower()}.{data['mbiemri'].lower()}@optikarburant.al",
                    'leje_drejtimi_skadon': expiry_date,
                    'leje_adr': data['adr'],
                    'ore_punes_maksimale_ditor': 8,
                    'ore_drejtimi_maksimale_ditor': 6,
                    'data_punesimit': timezone.now().date() - timedelta(days=random.randint(30, 1095)),
                }
            )
            drivers.append(driver)
            if created:
                print(f"  ✓ Created driver: {driver.emri} {driver.mbiemri}")
        except Exception as e:
            print(f"  ⚠ Driver creation issue: {e}")

    print(f"✓ Created {len(drivers)} drivers")
    return drivers


def create_trucks_and_compartments(drivers):
    """Create trucks with compartments"""
    print("Creating trucks and compartments...")

    trucks_data = [
        {'targa': 'AA001KK', 'modeli': 'Mercedes Actros 2545', 'year': 2020, 'compartments': [8000, 8000, 6000], 'has_pump': True, 'has_meter': True},
        {'targa': 'AA002KK', 'modeli': 'Volvo FH16 540', 'year': 2019, 'compartments': [10000, 10000], 'has_pump': True, 'has_meter': True},
        {'targa': 'AA003KK', 'modeli': 'Scania R450', 'year': 2021, 'compartments': [7000, 7000, 7000], 'has_pump': False, 'has_meter': True},
        {'targa': 'AA004KK', 'modeli': 'MAN TGX 26.440', 'year': 2018, 'compartments': [12000, 8000], 'has_pump': True, 'has_meter': False},
        {'targa': 'AA005KK', 'modeli': 'DAF XF 480', 'year': 2022, 'compartments': [9000, 9000], 'has_pump': True, 'has_meter': True},
        {'targa': 'AA006KK', 'modeli': 'Iveco Stralis 460', 'year': 2017, 'compartments': [6000, 6000, 6000, 4000], 'has_pump': False, 'has_meter': True},
    ]

    trucks = []
    for i, data in enumerate(trucks_data):
        try:
            # Assign driver (cycle through available drivers)
            driver = drivers[i % len(drivers)] if drivers else None

            # Calculate truck specifications
            total_capacity = sum(data['compartments'])
            max_weight = 40.0 if total_capacity > 15000 else 32.0
            length = 16.5 if len(data['compartments']) > 2 else 12.0

            truck, created = Kamion.objects.get_or_create(
                targa=data['targa'],
                defaults={
                    'modeli': data['modeli'],
                    'viti_prodhimit': data['year'],
                    'shofer_aktual': driver,
                    'pesha_maksimale_bruto_ton': max_weight,
                    'gjatesia_totale_m': length,
                    'eshte_trailer': True,
                    'ka_pompe': data['has_pump'],
                    'ka_kontaliter': data['has_meter'],
                    'ka_gps': True,
                    'statusi': 'i_lire',
                    'odometri_aktual_km': random.randint(50000, 200000),
                    'km_mirembajtjes_rradheses': random.randint(10000, 30000),
                    'konsumi_mesatar_l_100km': random.uniform(32.0, 38.0),
                }
            )

            # Create compartments for this truck
            if created:
                for comp_num, capacity in enumerate(data['compartments'], 1):
                    Particion.objects.create(
                        kamion=truck,
                        numri_i_dhomes=comp_num,
                        kapaciteti=capacity,
                        produkti_aktual=None,  # Empty initially
                        eshte_i_pastruar=True,
                        sasia_aktuale=0,
                    )
                print(f"  ✓ Created truck: {truck.targa} with {len(data['compartments'])} compartments")

            trucks.append(truck)

        except Exception as e:
            print(f"  ⚠ Truck creation issue: {e}")

    print(f"✓ Created {len(trucks)} trucks")
    return trucks


def create_station_tanks(stations, products):
    """Create fuel tanks at stations"""
    print("Creating station fuel tanks...")

    tanks_created = 0
    for station in stations:
        try:
            # Each station gets 2-4 different fuel types
            num_products = random.randint(2, min(4, len(products)))
            selected_products = random.sample(products, num_products)

            for i, product in enumerate(selected_products, 1):
                # Tank capacity based on station size and product type
                if 'Tiranë' in station.emri or 'Durrës' in station.emri:
                    capacity = random.randint(15000, 25000)  # Larger stations
                else:
                    capacity = random.randint(8000, 15000)   # Smaller stations

                # Current level (30-80% of capacity)
                current_level = capacity * random.uniform(0.3, 0.8)

                # Safety and reorder levels
                safety_level = capacity * 0.15  # 15% safety stock
                reorder_level = capacity * 0.35  # 35% reorder level

                tank, created = Depozite.objects.get_or_create(
                    stacion=station,
                    produkt=product,
                    numri_tankut=f"T{i:02d}",
                    defaults={
                        'kapaciteti_total': capacity,
                        'sasia_aktuale': current_level,
                        'niveli_minimal_sigurise': safety_level,
                        'niveli_i_porosise': reorder_level,
                        'konsumi_mesatar_ditor': random.randint(500, 2000),
                        'sasia_minimale_dorezimi': 2000,
                        'perqindja_maksimale_mbushjes': 95,
                    }
                )

                if created:
                    tanks_created += 1

        except Exception as e:
            print(f"  ⚠ Tank creation issue for {station.emri}: {e}")

    print(f"✓ Created {tanks_created} fuel tanks across all stations")
    return tanks_created


def create_sample_orders(stations, products):
    """Create sample fuel orders for optimization"""
    print("Creating sample fuel orders...")

    orders = []
    # Create orders for stations that need fuel (low inventory)
    for station in stations[:15]:  # First 15 stations get orders
        try:
            # Get tanks for this station that need refilling
            tanks = Depozite.objects.filter(stacion=station)

            for tank in tanks:
                # Check if tank needs refilling (below reorder level)
                if tank.sasia_aktuale <= tank.niveli_i_porosise:
                    # Calculate order quantity
                    max_fill = tank.kapaciteti_total * (tank.perqindja_maksimale_mbushjes / 100)
                    order_quantity = max_fill - tank.sasia_aktuale

                    # Due date: tomorrow to 3 days from now
                    due_date = timezone.now() + timedelta(days=random.randint(1, 3))

                    # Check if order already exists for this tank
                    existing_order = Porosi.objects.filter(
                        stacion=station,
                        produkt=tank.produkt,
                        depozite=tank,
                        statusi__in=['e_hapur', 'e_planifikuar', 'ne_transport']
                    ).first()

                    if not existing_order:
                        order = Porosi.objects.create(
                            stacion=station,
                            produkt=tank.produkt,
                            depozite=tank,
                            sasia_e_kerkuar=order_quantity,
                            sasia_e_miratuar=order_quantity,
                            prioriteti=random.choice(['e_larte', 'normale', 'e_ulet']),
                            data_afati=due_date,
                            koha_preferuar_fillimi=time(8, 0),
                            koha_preferuar_mbarimi=time(16, 0),
                            statusi='e_hapur',  # Changed from 'e_miratuar' to 'e_hapur'
                        )
                        orders.append(order)
                        print(f"  ✓ Created order: {station.emri} - {tank.produkt.emri} ({order_quantity:.0f}L)")
                    else:
                        print(f"  ⚠ Order already exists for {station.emri} - {tank.produkt.emri}")

        except Exception as e:
            print(f"  ⚠ Order creation issue for {station.emri}: {e}")

    # Add some manual orders for testing
    print("Creating additional manual orders for testing...")
    manual_orders_data = [
        {'station_name': 'Stacioni Qender Tiranë', 'product': 'Naftë D2', 'quantity': 5000, 'priority': 'e_larte'},
        {'station_name': 'Stacioni Kombinat', 'product': 'Benzinë 95', 'quantity': 3000, 'priority': 'normale'},
        {'station_name': 'Stacioni Durrës Port', 'product': 'Naftë D2', 'quantity': 8000, 'priority': 'e_larte'},
        {'station_name': 'Stacioni Shkodër Qender', 'product': 'Benzinë 95', 'quantity': 4000, 'priority': 'normale'},
        {'station_name': 'Stacioni Elbasan Qender', 'product': 'Gaz i Lëngshëm (LPG)', 'quantity': 2500, 'priority': 'e_larte'},
    ]

    for order_data in manual_orders_data:
        try:
            station = Stacion.objects.filter(emri=order_data['station_name']).first()
            product = Produkt.objects.filter(emri=order_data['product']).first()

            if station and product:
                # Find a tank for this product at this station
                tank = Depozite.objects.filter(stacion=station, produkt=product).first()

                if tank:
                    # Check if order already exists
                    existing_order = Porosi.objects.filter(
                        stacion=station,
                        produkt=product,
                        depozite=tank,
                        statusi__in=['e_hapur', 'e_planifikuar', 'ne_transport']
                    ).first()

                    if not existing_order:
                        due_date = timezone.now() + timedelta(days=random.randint(1, 2))

                        order = Porosi.objects.create(
                            stacion=station,
                            produkt=product,
                            depozite=tank,
                            sasia_e_kerkuar=order_data['quantity'],
                            sasia_e_miratuar=order_data['quantity'],
                            prioriteti=order_data['priority'],
                            data_afati=due_date,
                            koha_preferuar_fillimi=time(8, 0),
                            koha_preferuar_mbarimi=time(16, 0),
                            statusi='e_hapur',
                            eshte_automatike=False,
                            shenimet=f"Manual test order - {order_data['priority']} priority"
                        )
                        orders.append(order)
                        print(f"  ✓ Created manual order: {station.emri} - {product.emri} ({order_data['quantity']}L)")
                    else:
                        print(f"  ⚠ Manual order already exists for {station.emri} - {product.emri}")
                else:
                    print(f"  ⚠ No tank found for {product.emri} at {station.emri}")
            else:
                print(f"  ⚠ Station or product not found for manual order")

        except Exception as e:
            print(f"  ⚠ Manual order creation issue: {e}")

    print(f"✓ Created {len(orders)} total fuel orders")
    return orders


def main():
    """Main function to populate the database"""
    print("🇦🇱 OptiKarburant Albania - Complete Sample Data Population")
    print("=" * 60)

    try:
        # Clear existing data
        clear_existing_data()

        # Create products
        products = create_products()

        # Create central depot
        depot = create_central_depot()

        # Create stations
        stations = create_stations()

        # Create drivers
        drivers = create_drivers()

        # Create trucks and compartments
        trucks = create_trucks_and_compartments(drivers)

        # Create station fuel tanks
        tanks_count = create_station_tanks(stations, products)

        # Create sample orders
        orders = create_sample_orders(stations, products)

        print("\n" + "=" * 60)
        print("✅ Complete sample data population finished successfully!")
        print(f"📊 Summary:")
        print(f"   • Products: {len(products)}")
        print(f"   • Central Depot: 1")
        print(f"   • Stations: {len(stations)}")
        print(f"   • Drivers: {len(drivers)}")
        print(f"   • Trucks: {len(trucks)}")
        print(f"   • Fuel Tanks: {tanks_count}")
        print(f"   • Orders: {len(orders)}")
        print("\n🚀 Ready for route optimization testing!")
        print("🌐 Access Django admin: http://localhost:8000/admin")
        print("🔧 API endpoints: http://localhost:8000/api/")

    except Exception as e:
        print(f"\n❌ Error during data population: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
