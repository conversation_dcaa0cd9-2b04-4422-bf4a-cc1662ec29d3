// static/js/main.js - Main JavaScript for OptiKarburant

/**
 * OptiKarburant - Main JavaScript Module
 * Provides functionality for maps, real-time updates, mobile interface, and more
 */

const OptiKarburant = {
    // Configuration
    config: {
        map: {
            defaultZoom: 10,
            centerLat: 41.3275,
            centerLng: 19.8189,
            tileServer: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        },
        refreshInterval: 30000, // 30 seconds
        apiEndpoints: {
            stationStatus: '/dashboard/api/station/{id}/status/',
            systemStatus: '/api/system/dashboard-stats/',
            optimizationProgress: '/optimization/api/progress/{taskId}/'
        }
    },

    // Global variables
    maps: {},
    intervals: {},
    isOnline: navigator.onLine,

    // Initialize application
    init() {
        console.log('🚀 Initializing OptiKarburant...');
        
        this.setupEventListeners();
        this.initializeMaps();
        this.startRealTimeUpdates();
        this.setupMobileFeatures();
        this.setupNotifications();
        this.setupOfflineHandling();
        
        console.log('✅ OptiKarburant initialized successfully');
    },

    // Event listeners setup
    setupEventListeners() {
        // Navigation active state
        this.highlightActiveNavigation();
        
        // Form enhancements
        this.enhanceForms();
        
        // Auto-refresh toggles
        this.setupAutoRefresh();
        
        // Search functionality
        this.setupSearch();
        
        // Mobile gestures
        this.setupMobileGestures();
    },

    // Highlight active navigation
    highlightActiveNavigation() {
        const currentPath = window.location.pathname;
        document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });
    },

    // Map functionality
    Maps: {
        // Initialize all maps on page
        initialize() {
            document.querySelectorAll('.map-container').forEach(container => {
                OptiKarburant.Maps.createMap(container);
            });
        },

        // Create a new map instance
        createMap(container) {
            const mapId = container.id || `map_${Date.now()}`;
            const config = OptiKarburant.config.map;
            
            // Create map
            const map = L.map(container, {
                center: [config.centerLat, config.centerLng],
                zoom: config.defaultZoom,
                zoomControl: true,
                attributionControl: true
            });

            // Add tile layer
            L.tileLayer(config.tileServer, {
                attribution: config.attribution,
                maxZoom: 18
            }).addTo(map);

            // Store map reference
            OptiKarburant.maps[mapId] = map;

            // Load map data if available
            const dataUrl = container.dataset.dataUrl;
            if (dataUrl) {
                OptiKarburant.Maps.loadMapData(map, dataUrl);
            }

            return map;
        },

        // Load map data from API
        async loadMapData(map, dataUrl) {
            try {
                const response = await fetch(dataUrl);
                const data = await response.json();
                
                OptiKarburant.Maps.addStationsToMap(map, data.stations || []);
                OptiKarburant.Maps.addRoutesToMap(map, data.routes || []);
                
            } catch (error) {
                console.error('Failed to load map data:', error);
                OptiKarburant.showNotification('Failed to load map data', 'error');
            }
        },

        // Add stations to map
        addStationsToMap(map, stations) {
            const stationGroup = L.layerGroup().addTo(map);
            
            stations.forEach(station => {
                const color = this.getStationColor(station.status);
                const marker = L.circleMarker([station.lat, station.lng], {
                    radius: 8,
                    fillColor: color,
                    color: '#fff',
                    weight: 2,
                    opacity: 1,
                    fillOpacity: 0.8
                });

                // Popup content
                const popupContent = `
                    <div class="station-popup">
                        <h6>${station.name}</h6>
                        <p><strong>Code:</strong> ${station.code}</p>
                        <p><strong>Address:</strong> ${station.address}</p>
                        <p><strong>Tanks:</strong> ${station.total_tanks}</p>
                        ${station.critical_tanks > 0 ? 
                            `<span class="badge bg-danger">Critical: ${station.critical_tanks}</span>` : 
                            '<span class="badge bg-success">Normal</span>'
                        }
                        <br><br>
                        <a href="/stations/${station.id}/" class="btn btn-sm btn-primary">
                            View Details
                        </a>
                    </div>
                `;

                marker.bindPopup(popupContent);
                stationGroup.addLayer(marker);
            });

            // Fit map to show all stations
            if (stations.length > 0) {
                const group = new L.featureGroup(stationGroup.getLayers());
                map.fitBounds(group.getBounds().pad(0.1));
            }
        },

        // Add routes to map
        addRoutesToMap(map, routes) {
            const routeGroup = L.layerGroup().addTo(map);
            
            routes.forEach(route => {
                if (route.coordinates && route.coordinates.length > 0) {
                    const routeLine = L.polyline(route.coordinates, {
                        color: this.getRouteColor(route.status),
                        weight: 4,
                        opacity: 0.7
                    });

                    routeLine.bindPopup(`
                        <div>
                            <h6>Route ${route.number}</h6>
                            <p><strong>Truck:</strong> ${route.truck}</p>
                            <p><strong>Status:</strong> ${route.status}</p>
                            <p><strong>Distance:</strong> ${route.distance}km</p>
                        </div>
                    `);

                    routeGroup.addLayer(routeLine);
                }
            });
        },

        // Get station marker color based on status
        getStationColor(status) {
            switch (status) {
                case 'critical': return '#dc3545';
                case 'warning': return '#ffc107';
                case 'normal': return '#28a745';
                default: return '#6c757d';
            }
        },

        // Get route color based on status
        getRouteColor(status) {
            switch (status) {
                case 'completed': return '#28a745';
                case 'in_progress': return '#ffc107';
                case 'planned': return '#007bff';
                default: return '#6c757d';
            }
        },

        // Update map data
        async updateMapData(mapId, dataUrl) {
            const map = OptiKarburant.maps[mapId];
            if (map) {
                await this.loadMapData(map, dataUrl);
            }
        }
    },

    // Real-time updates
    RealTime: {
        // Start real-time updates
        start() {
            // Dashboard stats update
            this.updateDashboardStats();
            OptiKarburant.intervals.dashboard = setInterval(() => {
                this.updateDashboardStats();
            }, OptiKarburant.config.refreshInterval);

            // Station status updates
            this.updateStationStatuses();
            OptiKarburant.intervals.stations = setInterval(() => {
                this.updateStationStatuses();
            }, OptiKarburant.config.refreshInterval);
        },

        // Stop real-time updates
        stop() {
            Object.values(OptiKarburant.intervals).forEach(interval => {
                clearInterval(interval);
            });
            OptiKarburant.intervals = {};
        },

        // Update dashboard statistics
        async updateDashboardStats() {
            try {
                const response = await fetch(OptiKarburant.config.apiEndpoints.systemStatus);
                const data = await response.json();
                
                // Update metric cards
                this.updateMetricCard('total-stations', data.total_stations);
                this.updateMetricCard('active-trucks', data.active_trucks);
                this.updateMetricCard('open-orders', data.open_orders);
                this.updateMetricCard('critical-tanks', data.critical_tanks);
                this.updateMetricCard('today-routes', data.today_routes);
                
                // Update timestamp
                const lastUpdatedElement = document.getElementById('last-updated');
                if (lastUpdatedElement) {
                    lastUpdatedElement.textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
                }
                    
            } catch (error) {
                console.error('Failed to update dashboard stats:', error);
            }
        },

        // Update metric card value
        updateMetricCard(cardId, value) {
            const element = document.getElementById(cardId);
            if (element) {
                const currentValue = parseInt(element.textContent) || 0;
                if (currentValue !== value) {
                    element.textContent = value;
                    element.parentElement.classList.add('pulse');
                    setTimeout(() => {
                        element.parentElement.classList.remove('pulse');
                    }, 1000);
                }
            }
        },

        // Update station statuses
        async updateStationStatuses() {
            const stationElements = document.querySelectorAll('[data-station-id]');
            
            for (const element of stationElements) {
                const stationId = element.dataset.stationId;
                try {
                    const response = await fetch(
                        OptiKarburant.config.apiEndpoints.stationStatus.replace('{id}', stationId)
                    );
                    const data = await response.json();
                    
                    // Update station status in UI
                    this.updateStationUI(element, data);
                    
                } catch (error) {
                    console.error(`Failed to update station ${stationId}:`, error);
                }
            }
        },

        // Update station UI elements
        updateStationUI(element, data) {
            // Update tank levels
            data.tanks.forEach(tank => {
                const tankElement = element.querySelector(`[data-tank-id="${tank.id}"]`);
                if (tankElement) {
                    const progressBar = tankElement.querySelector('.progress-bar');
                    if (progressBar) {
                        progressBar.style.width = `${tank.fill_percentage}%`;
                        progressBar.className = `progress-bar ${this.getTankProgressClass(tank.fill_percentage)}`;
                    }
                    
                    const quantityElement = tankElement.querySelector('.tank-quantity');
                    if (quantityElement) {
                        quantityElement.textContent = `${tank.current_quantity.toLocaleString()}L`;
                    }
                }
            });
        },

        // Get progress bar class based on fill percentage
        getTankProgressClass(percentage) {
            if (percentage < 25) return 'bg-danger';
            if (percentage < 50) return 'bg-warning';
            return 'bg-success';
        }
    },

    // Mobile features
    Mobile: {
        // Setup mobile-specific features
        setup() {
            this.setupTouchGestures();
            this.setupPullToRefresh();
            this.setupOfflineMode();
        },

        // Touch gestures
        setupTouchGestures() {
            let touchStartX = 0;
            let touchStartY = 0;

            document.addEventListener('touchstart', (e) => {
                touchStartX = e.touches[0].clientX;
                touchStartY = e.touches[0].clientY;
            }, { passive: true });

            document.addEventListener('touchend', (e) => {
                const touchEndX = e.changedTouches[0].clientX;
                const touchEndY = e.changedTouches[0].clientY;
                
                const deltaX = touchEndX - touchStartX;
                const deltaY = touchEndY - touchStartY;
                
                // Swipe detection
                if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                    if (deltaX > 0) {
                        this.handleSwipeRight();
                    } else {
                        this.handleSwipeLeft();
                    }
                }
            }, { passive: true });
        },

        // Handle swipe right (back navigation)
        handleSwipeRight() {
            if (history.length > 1) {
                history.back();
            }
        },

        // Handle swipe left (forward navigation)
        handleSwipeLeft() {
            // Could implement forward navigation or other actions
        },

        // Pull to refresh
        setupPullToRefresh() {
            let startY = 0;
            let isPulling = false;

            document.addEventListener('touchstart', (e) => {
                if (window.scrollY === 0) {
                    startY = e.touches[0].clientY;
                    isPulling = true;
                }
            }, { passive: true });

            document.addEventListener('touchmove', (e) => {
                if (isPulling && window.scrollY === 0) {
                    const currentY = e.touches[0].clientY;
                    const pullDistance = currentY - startY;
                    
                    if (pullDistance > 100) {
                        this.triggerRefresh();
                        isPulling = false;
                    }
                }
            }, { passive: true });

            document.addEventListener('touchend', () => {
                isPulling = false;
            }, { passive: true });
        },

        // Trigger page refresh
        triggerRefresh() {
            OptiKarburant.showNotification('Refreshing...', 'info');
            location.reload();
        },

        // Setup offline mode
        setupOfflineMode() {
            window.addEventListener('online', () => {
                OptiKarburant.isOnline = true;
                OptiKarburant.showNotification('Connection restored', 'success');
                OptiKarburant.RealTime.start();
            });

            window.addEventListener('offline', () => {
                OptiKarburant.isOnline = false;
                OptiKarburant.showNotification('Working offline', 'warning');
                OptiKarburant.RealTime.stop();
            });
        }
    },

    // Optimization progress tracking
    Optimization: {
        // Track optimization progress
        trackProgress(taskId) {
            const progressContainer = document.getElementById('optimization-progress');
            if (!progressContainer) return;

            const interval = setInterval(async () => {
                try {
                    const response = await fetch(
                        OptiKarburant.config.apiEndpoints.optimizationProgress.replace('{taskId}', taskId)
                    );
                    const data = await response.json();
                    
                    this.updateProgressUI(data);
                    
                    if (data.state === 'SUCCESS' || data.state === 'FAILURE') {
                        clearInterval(interval);
                        this.handleProgressComplete(data);
                    }
                    
                } catch (error) {
                    console.error('Failed to get optimization progress:', error);
                    clearInterval(interval);
                }
            }, 2000);
        },

        // Update progress UI
        updateProgressUI(data) {
            const progressBar = document.getElementById('progress-bar');
            const statusText = document.getElementById('status-text');
            
            if (progressBar) {
                progressBar.style.width = `${data.current || 0}%`;
            }
            
            if (statusText) {
                statusText.textContent = data.status || 'Processing...';
            }
        },

        // Handle optimization completion
        handleProgressComplete(data) {
            if (data.state === 'SUCCESS') {
                OptiKarburant.showNotification('Optimization completed successfully!', 'success');
                
                // Redirect to results or refresh page
                setTimeout(() => {
                    window.location.href = '/optimization/routes/';
                }, 2000);
            } else {
                OptiKarburant.showNotification('Optimization failed', 'error');
            }
        }
    },

    // Utility functions
    Utils: {
        // Format numbers
        formatNumber(num) {
            return new Intl.NumberFormat().format(num);
        },

        // Format currency
        formatCurrency(amount, currency = 'EUR') {
            return new Intl.NumberFormat('en-EU', {
                style: 'currency',
                currency: currency
            }).format(amount);
        },

        // Format date
        formatDate(date) {
            return new Intl.DateTimeFormat().format(new Date(date));
        },

        // Format time
        formatTime(date) {
            return new Intl.DateTimeFormat('en-EU', {
                hour: '2-digit',
                minute: '2-digit'
            }).format(new Date(date));
        },

        // Debounce function
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Throttle function
        throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }
    },

    // Enhanced form functionality
    enhanceForms() {
        // Auto-save forms
        document.querySelectorAll('form[data-auto-save]').forEach(form => {
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('change', this.Utils.debounce(() => {
                    this.autoSaveForm(form);
                }, 1000));
            });
        });

        // Form validation
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                }
            });
        });
    },

    // Auto-save form data
    autoSaveForm(form) {
        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        localStorage.setItem(`form_${form.id}`, JSON.stringify(data));
        this.showNotification('Form auto-saved', 'info', 2000);
    },

    // Validate form
    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                this.showFieldError(input, 'This field is required');
                isValid = false;
            } else {
                this.clearFieldError(input);
            }
        });
        
        return isValid;
    },

    // Show field error
    showFieldError(input, message) {
        input.classList.add('is-invalid');
        
        let errorElement = input.parentNode.querySelector('.invalid-feedback');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'invalid-feedback';
            input.parentNode.appendChild(errorElement);
        }
        
        errorElement.textContent = message;
    },

    // Clear field error
    clearFieldError(input) {
        input.classList.remove('is-invalid');
        const errorElement = input.parentNode.querySelector('.invalid-feedback');
        if (errorElement) {
            errorElement.remove();
        }
    },

    // Setup auto-refresh
    setupAutoRefresh() {
        const refreshToggle = document.getElementById('auto-refresh-toggle');
        if (refreshToggle) {
            refreshToggle.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.RealTime.start();
                } else {
                    this.RealTime.stop();
                }
            });
        }
    },

    // Setup search functionality
    setupSearch() {
        const searchInputs = document.querySelectorAll('input[data-search]');
        
        searchInputs.forEach(input => {
            input.addEventListener('input', this.Utils.debounce((e) => {
                this.performSearch(e.target);
            }, 300));
        });
    },

    // Perform search
    performSearch(input) {
        const searchTerm = input.value.toLowerCase();
        const targetSelector = input.dataset.search;
        const targets = document.querySelectorAll(targetSelector);
        
        targets.forEach(target => {
            const text = target.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                target.style.display = '';
            } else {
                target.style.display = 'none';
            }
        });
    },

    // Setup mobile gestures
    setupMobileGestures() {
        if ('ontouchstart' in window) {
            this.Mobile.setup();
        }
    },

    // Notification system
    showNotification(message, type = 'info', duration = 5000) {
        // Remove existing notifications
        document.querySelectorAll('.custom-notification').forEach(el => el.remove());
        
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} custom-notification position-fixed`;
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 1050;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas ${this.getNotificationIcon(type)} me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }
    },

    // Get notification icon
    getNotificationIcon(type) {
        switch (type) {
            case 'success': return 'fa-check-circle';
            case 'error': return 'fa-exclamation-triangle';
            case 'warning': return 'fa-exclamation-circle';
            default: return 'fa-info-circle';
        }
    },

    // Setup notifications
    setupNotifications() {
        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    },

    // Show browser notification
    showBrowserNotification(title, options = {}) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, {
                icon: '/static/images/icon.png',
                badge: '/static/images/badge.png',
                ...options
            });
        }
    },

    // Setup offline handling
    setupOfflineHandling() {
        // Service Worker registration
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered:', registration);
                })
                .catch(error => {
                    console.log('SW registration failed:', error);
                });
        }
    },

    // Initialize maps
    initializeMaps() {
        if (typeof L !== 'undefined') {
            this.Maps.initialize();
        }
    },

    // Start real-time updates
    startRealTimeUpdates() {
        if (this.isOnline) {
            this.RealTime.start();
        }
    },

    // Setup mobile features
    setupMobileFeatures() {
        this.Mobile.setup();
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    OptiKarburant.init();
});

// Make OptiKarburant globally available
window.OptiKarburant = OptiKarburant;
