<!-- templates/dashboard/order_management.html - Order Management Dashboard -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .order-card {
        transition: transform 0.2s;
        border-left: 4px solid #dee2e6;
    }
    .order-card:hover {
        transform: translateY(-2px);
    }
    .order-card.priority-high {
        border-left-color: #dc3545;
    }
    .order-card.priority-medium {
        border-left-color: #ffc107;
    }
    .order-card.priority-low {
        border-left-color: #28a745;
    }
    .status-timeline {
        position: relative;
        padding-left: 20px;
    }
    .status-timeline::before {
        content: '';
        position: absolute;
        left: 6px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    .timeline-item {
        position: relative;
        margin-bottom: 10px;
    }
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -8px;
        top: 6px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #6c757d;
    }
    .timeline-item.active::before {
        background: #007bff;
    }
    .timeline-item.completed::before {
        background: #28a745;
    }
</style>
{% endblock %}

{% block page_icon %}<i class="fas fa-clipboard-list text-primary"></i>{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Order Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ order_stats.total_orders }}</h4>
                            <p class="mb-0">Total Orders</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clipboard-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ order_stats.pending_orders }}</h4>
                            <p class="mb-0">Pending</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ order_stats.in_progress_orders }}</h4>
                            <p class="mb-0">In Progress</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-truck fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ order_stats.completed_today }}</h4>
                            <p class="mb-0">Completed Today</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-md-8">
            <form method="get" class="d-flex gap-2">
                <input type="text" name="search" class="form-control" 
                       placeholder="Search orders by ID, station, or product..." 
                       value="{{ filters.search|default:'' }}">
                <select name="status" class="form-select" style="max-width: 150px;">
                    <option value="">All Status</option>
                    <option value="pending" {% if filters.status == 'pending' %}selected{% endif %}>Pending</option>
                    <option value="confirmed" {% if filters.status == 'confirmed' %}selected{% endif %}>Confirmed</option>
                    <option value="in_progress" {% if filters.status == 'in_progress' %}selected{% endif %}>In Progress</option>
                    <option value="completed" {% if filters.status == 'completed' %}selected{% endif %}>Completed</option>
                    <option value="cancelled" {% if filters.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                </select>
                <select name="priority" class="form-select" style="max-width: 120px;">
                    <option value="">All Priority</option>
                    <option value="high" {% if filters.priority == 'high' %}selected{% endif %}>High</option>
                    <option value="medium" {% if filters.priority == 'medium' %}selected{% endif %}>Medium</option>
                    <option value="low" {% if filters.priority == 'low' %}selected{% endif %}>Low</option>
                </select>
                <input type="date" name="date_from" class="form-control" style="max-width: 150px;"
                       value="{{ filters.date_from }}">
                <input type="date" name="date_to" class="form-control" style="max-width: 150px;"
                       value="{{ filters.date_to }}">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Filter
                </button>
                <a href="{% url 'dashboard:order_management' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </form>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#newOrderModal">
                <i class="fas fa-plus"></i> New Order
            </button>
        </div>
    </div>

    <!-- Orders List -->
    <div class="row">
        {% for order in orders %}
        <div class="col-lg-6 mb-4">
            <div class="card order-card priority-{{ order.prioriteti|lower }} h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <strong>Order #{{ order.id }}</strong>
                        <span class="badge bg-{{ order.status_color }} ms-2">{{ order.get_statusi_display }}</span>
                    </h6>
                    <small class="text-muted">{{ order.data_krijimit|date:"M d, Y H:i" }}</small>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-8">
                            <strong>{{ order.stacion.emri }}</strong><br>
                            <small class="text-muted">{{ order.stacion.adresa|truncatechars:40 }}</small>
                        </div>
                        <div class="col-4 text-end">
                            <span class="badge bg-{{ order.priority_color }}">
                                {{ order.get_prioriteti_display }} Priority
                            </span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted">Product:</small><br>
                            <strong>{{ order.produkt.emri }}</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Quantity:</small><br>
                            <strong>{{ order.sasia_e_kerkuar|floatformat:0 }}L</strong>
                        </div>
                    </div>

                    {% if order.data_e_deshiruar %}
                    <div class="mb-3">
                        <small class="text-muted">Requested Delivery:</small><br>
                        <strong>{{ order.data_e_deshiruar|date:"M d, Y H:i" }}</strong>
                        {% if order.is_urgent %}
                            <span class="badge bg-danger ms-2">Urgent</span>
                        {% endif %}
                    </div>
                    {% endif %}

                    {% if order.plan_rruge %}
                    <div class="mb-3">
                        <small class="text-muted">Assigned Route:</small><br>
                        <strong>{{ order.plan_rruge.emri }}</strong>
                        {% if order.plan_rruge.kamion %}
                            <br><small class="text-muted">Truck: {{ order.plan_rruge.kamion.emri }}</small>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Status Timeline -->
                    <div class="status-timeline">
                        <div class="timeline-item {% if order.statusi != 'pending' %}completed{% endif %}">
                            <small>Order Created</small>
                        </div>
                        <div class="timeline-item {% if order.statusi == 'confirmed' or order.statusi == 'in_progress' or order.statusi == 'completed' %}completed{% elif order.statusi == 'pending' %}active{% endif %}">
                            <small>Confirmed</small>
                        </div>
                        <div class="timeline-item {% if order.statusi == 'in_progress' or order.statusi == 'completed' %}completed{% elif order.statusi == 'confirmed' %}active{% endif %}">
                            <small>In Progress</small>
                        </div>
                        <div class="timeline-item {% if order.statusi == 'completed' %}completed{% elif order.statusi == 'in_progress' %}active{% endif %}">
                            <small>Completed</small>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="#" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> Details
                        </a>
                        {% if order.statusi == 'pending' %}
                            <div>
                                <a href="#" class="btn btn-sm btn-success">
                                    <i class="fas fa-check"></i> Confirm
                                </a>
                                <a href="#" class="btn btn-sm btn-outline-danger">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        {% elif order.statusi == 'confirmed' %}
                            <a href="#" class="btn btn-sm btn-primary">
                                <i class="fas fa-route"></i> Assign Route
                            </a>
                        {% elif order.statusi == 'in_progress' %}
                            <a href="#" class="btn btn-sm btn-info">
                                <i class="fas fa-map-marked-alt"></i> Track
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No orders found</h5>
                <p class="text-muted">Try adjusting your search criteria or create a new order.</p>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if orders.has_other_pages %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="Order pagination">
                <ul class="pagination justify-content-center">
                    {% if orders.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ orders.previous_page_number }}">Previous</a>
                        </li>
                    {% endif %}
                    
                    {% for num in orders.paginator.page_range %}
                        {% if orders.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if orders.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ orders.next_page_number }}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>

<!-- New Order Modal -->
<div class="modal fade" id="newOrderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Order</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="newOrderForm">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Station</label>
                            <select class="form-select" name="station" required>
                                <option value="">Select Station</option>
                                <!-- Stations will be populated via AJAX -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Product</label>
                            <select class="form-select" name="product" required>
                                <option value="">Select Product</option>
                                <!-- Products will be populated via AJAX -->
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Quantity (Liters)</label>
                            <input type="number" class="form-control" name="quantity" required min="1">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Priority</label>
                            <select class="form-select" name="priority" required>
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Requested Delivery Date</label>
                        <input type="datetime-local" class="form-control" name="delivery_date">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea class="form-control" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitNewOrder()">Create Order</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Load stations and products when modal opens
document.getElementById('newOrderModal').addEventListener('show.bs.modal', function() {
    loadStations();
    loadProducts();
});

function loadStations() {
    fetch('{% url "dashboard:get_stations_api" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const stationSelect = document.querySelector('select[name="station"]');
                stationSelect.innerHTML = '<option value="">Select Station</option>';

                data.stations.forEach(station => {
                    const option = document.createElement('option');
                    option.value = station.id;
                    option.textContent = `${station.emri} - ${station.adresa}`;
                    stationSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading stations:', error);
            showAlert('Error loading stations', 'danger');
        });
}

function loadProducts() {
    fetch('{% url "dashboard:get_products_api" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const productSelect = document.querySelector('select[name="product"]');
                productSelect.innerHTML = '<option value="">Select Product</option>';

                data.products.forEach(product => {
                    const option = document.createElement('option');
                    option.value = product.id;
                    option.textContent = `${product.emri} (${product.densiteti} kg/L)`;
                    productSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading products:', error);
            showAlert('Error loading products', 'danger');
        });
}

function submitNewOrder() {
    const form = document.getElementById('newOrderForm');
    const formData = new FormData(form);

    // Validate form
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Prepare data
    const orderData = {
        station: formData.get('station'),
        product: formData.get('product'),
        quantity: formData.get('quantity'),
        priority: formData.get('priority'),
        delivery_date: formData.get('delivery_date'),
        notes: formData.get('notes')
    };

    // Show loading state
    const submitBtn = document.querySelector('#newOrderModal .btn-primary');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
    submitBtn.disabled = true;

    // Submit order
    fetch('{% url "dashboard:create_order" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(orderData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`Order ${data.order.number} created successfully!`, 'success');

            // Close modal and reset form
            const modal = bootstrap.Modal.getInstance(document.getElementById('newOrderModal'));
            modal.hide();
            form.reset();

            // Refresh page after short delay
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert(data.error || 'Failed to create order', 'danger');
        }
    })
    .catch(error => {
        console.error('Error creating order:', error);
        showAlert('Network error. Please try again.', 'danger');
    })
    .finally(() => {
        // Reset button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function showAlert(message, type) {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at top of modal body
    const modalBody = document.querySelector('#newOrderModal .modal-body');
    modalBody.insertBefore(alertDiv, modalBody.firstChild);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Auto-refresh every 60 seconds
setInterval(function() {
    if (!document.querySelector('.modal.show')) {
        location.reload();
    }
}, 60000);
</script>
{% endblock %}
